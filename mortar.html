<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mortar Team Display</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
          integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
          crossorigin=""/>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <!-- Navigation Header -->
        <div class="screen-navigation">
            <h1>Mortar Team Display</h1>
            <div class="nav-buttons">
                <button type="button" id="observer-nav-btn" class="nav-button" onclick="window.location.href='observer.html'">Observer Screen</button>
                <button type="button" id="mortar-nav-btn" class="nav-button active" disabled>Mortar Screen</button>
            </div>
        </div>

        <button type="button" class="collapsible">How to Use the Mortar Team Screen</button>
        <div class="content">
            <ol>
                <li><strong>Set Mortar Position:</strong> Click on the map to set the current location of the mortar. A marker will appear on the map, and the coordinates will be displayed under "Mortar Settings."</li>
                <li><strong>Select Weapon and Ammunition:</strong>
                    <ul>
                        <li>Choose the "Mortar Type" from the dropdown menu (e.g., 60mm M224).</li>
                        <li>Once a mortar type is selected, the "Ammunition Type" dropdown will be enabled. Choose the appropriate ammunition type.</li>
                    </ul>
                </li>
                <li><strong>View Received Targets:</strong> The "Received Targets" section will display targets that have been sent by the Observer.</li>
                <li><strong>Select a Target:</strong> Click on a target in the "Received Targets" list to select it. The selected target will be highlighted on the list and might also be indicated on the map.</li>
                <li><strong>Calculate Firing Solution:</strong> Once the mortar position, weapon, ammunition, and a target are selected, the "Calculate Solution" button will be enabled. Click this button to calculate the firing solution.</li>
                <li><strong>View Firing Solution:</strong> The calculated firing solution (Distance, Bearing, Elevation, Deflection) will be displayed in the "Firing Solution" area with a confirmation.</li>
                <li><strong>Refresh Targets:</strong> If you expect new targets, you can click the "Refresh Targets" button to check for updates.</li>
                <li><strong>Clear Targets:</strong> Click the "Clear Targets" button to remove all received targets from the list.</li>
            </ol>
        </div>

        <h1>Mortar Team - Target Display</h1>
        <p class="intro">Set the mortar position by clicking on the map, select the mortar and ammunition type, choose a target from the list, and then calculate the firing solution.</p>

        <div class="map-controls-wrapper">
            <div id="mortar-map-container">
                <div id="mortar-map"></div>
                <div id="map-legend" class="collapsed">
                    <h4>Target Type Legend</h4>
                    <div class="legend-content">
                    <div class="legend-items">
                        <div class="legend-item">
                            <div class="legend-tank"></div>
                            <span>Tank</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-infantry"></div>
                            <span>Infantry</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-mg"></div>
                            <span>MG Placement</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-artillery"></div>
                            <span>Artillery</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-antiair">
                                <div class="cross-horizontal"></div>
                                <div class="cross-vertical"></div>
                            </div>
                            <span>Anti-Aircraft</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-recon">
                                <svg width="20" height="20" viewBox="0 0 20 20">
                                    <polygon points="5,0 15,0 20,10 15,20 5,20 0,10" fill="#777" stroke="#777" stroke-width="1" />
                                </svg>
                            </div>
                            <span>Reconnaissance</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-apc"></div>
                            <span>APC</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-heli">
                                <svg width="20" height="20" viewBox="0 0 20 20">
                                    <polygon points="10,0 20,7 16,20 4,20 0,7" fill="#777" stroke="#777" stroke-width="1" />
                                </svg>
                            </div>
                            <span>Helicopter</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-command">
                                <svg width="20" height="20" viewBox="0 0 20 20">
                                    <polygon points="10,0 13,7 20,7 15,11 17,18 10,14 3,18 5,11 0,7 7,7" fill="#777" stroke="#777" stroke-width="1" />
                                </svg>
                            </div>
                            <span>Command Post</span>
                        </div>
                    </div>
                    <div class="force-type-indicators">
                        <div class="legend-item">
                            <div class="legend-icon legend-friendly"></div>
                            <span>Friendly</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-icon legend-enemy"></div>
                            <span>Enemy</span>
                        </div>
                    </div>
                    </div>
                </div>
            </div>

            <div id="mortar-controls-container">
            <div class="control-panel">
                <section id="mortar-settings">
                    <h3>Mortar Settings</h3>
                    <div class="control-section">
                        <label>Mortar Position (Click on the map to set):</label>
                        <div id="current-mortar-pos">Current Position: Not Set</div>
                    </div>
                    <div class="control-section weapon-selection">
                        <div class="form-group">
                            <label for="mortar-type">Mortar Type:</label>
                            <select id="mortar-type">
                                <option value="" selected disabled>-- Select Mortar --</option>
                                <option value="60mm M224">60mm M224</option>
                                <option value="81mm M252">81mm M252</option>
                                <option value="120mm M120/M121">120mm M120/M121</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="ammo-type">Ammunition Type:</label>
                            <select id="ammo-type" disabled>
                                <option value="" selected disabled>-- Select Mortar First --</option>
                            </select>
                        </div>
                    </div>
                </section>

                <section id="calculation-area">
                    <h3>Firing Solution</h3>
                    <div class="control-section calculation-controls">
                        <button type="button" id="calculate-solution-button" disabled title="Set mortar position, select weapon/ammo, and select target first">Calculate Solution</button>
                        <div id="calculated-solution-display">
                            <p><em>Select position, weapon, ammo, and target to calculate.</em></p>
                        </div>
                    </div>
                </section>
            </div>

            <div class="control-panel">
                <section id="target-display-area">
                    <h2>Received Targets</h2>
                    <div class="target-list-controls">
                        <button type="button" id="refresh-targets-button">Refresh Targets</button>
                        <button type="button" id="clear-targets-button-mortar">Clear Targets</button>
                        <button type="button" id="test-selection-button" onclick="testTargetSelection()">🔧 Test Selection</button>
                        <button type="button" id="debug-calc-button" onclick="debugCalculateButton()">🔍 Debug Calc</button>
                        <button type="button" id="force-enable-button" onclick="forceEnableCalculate()">⚡ Force Enable</button>
                        <button type="button" id="retro-mode-toggle" onclick="toggleRetroMode()">Retro Mode</button>
                    </div>
                    <ul id="received-targets-list">
                        <li>Waiting for target data...</li>
                    </ul>
                </section>
            </div>
        </div>
        </div>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>
    <script src="https://cdn.jsdelivr.net/npm/mgrs-js@1.0.1/mgrs.min.js"></script>
    <script src="mortar.js"></script>

</body>
</html>