// --- Constants and Global Variables ---
const initialCoords = [33.4735, -82.0740]; // Martinez, GA approx.
const mortarMap = L.map('mortar-map').setView(initialCoords, 13);
const receivedTargetsList = document.getElementById('received-targets-list');
const refreshButton = document.getElementById('refresh-targets-button');
const clearTargetsButton = document.getElementById('clear-targets-button-mortar');
let displayedMarkers = []; // Array to keep track of received target markers

// --- Mortar Position Elements ---
const mortarPosInput = document.getElementById('mortar-location-input'); // Ref kept even if hidden
const setMortarPosButton = document.getElementById('set-mortar-pos-button'); // Ref kept even if hidden
const currentMortarPosDisplay = document.getElementById('current-mortar-pos');
let mortarPosition = null; // L.latLng of the mortar
let mortarPositionMarker = null; // L.marker for the mortar

// --- Weapon Selection Elements & Data ---
const mortarTypeSelect = document.getElementById('mortar-type');
const ammoTypeSelect = document.getElementById('ammo-type');
let selectedMortarType = null;
let selectedAmmoType = null;

const mortarAmmoData = {
    '60mm M224': ['M720 HE', 'M722 Smoke WP', 'M888 HE', 'M767 IR Illum', 'M721 Practice'],
    '81mm M252': ['M821 HE', 'M889A1 HE', 'M819 Smoke RP', 'M853A1 Illum', 'M879 Practice'],
    '120mm M120/M121': ['M934 HE', 'M933 HE', 'M929 Smoke WP', 'M930 Illum', 'M931 Practice']
};

// --- Target Selection and Calculation Elements ---
const calculateButton = document.getElementById('calculate-solution-button');
const solutionDisplay = document.getElementById('calculated-solution-display');
let selectedTargetData = null; // Holds data of the target selected from the list
let selectedListItem = null; // Holds the reference to the selected LI element
let selectedTargetMarker = null; // Holds the reference to the selected target marker

// --- Initialization ---
L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
    attribution: 'Map data &copy; <a href="https://www.openstreetmap.org/">OpenStreetMap</a> contributors',
    maxZoom: 19,
}).addTo(mortarMap);

const fetchApiUrl = 'http://localhost:3000/api/getTargets'; // Points to your local server

// Demo mode - works without backend server
const DEMO_MODE = true; // Set to true for GitHub demo

// Status management variables
let connectionStatus = 'connecting';
let mortarReadyStatus = 'not-ready';

// Range rings variables
let rangeRings = [];
let showRangeRings = false;

// --- Status Management Functions ---
function updateConnectionStatus(status) {
    connectionStatus = status;
    const statusElement = document.getElementById('connection-status');
    const iconElement = document.getElementById('connection-icon');
    const textElement = document.getElementById('connection-text');

    if (!statusElement) return;

    // Remove all status classes
    statusElement.classList.remove('connected', 'disconnected', 'connecting');

    switch (status) {
        case 'connected':
            statusElement.classList.add('connected');
            textElement.textContent = 'Connected';
            break;
        case 'disconnected':
            statusElement.classList.add('disconnected');
            textElement.textContent = 'Disconnected';
            break;
        case 'connecting':
        default:
            statusElement.classList.add('connecting');
            textElement.textContent = 'Connecting...';
            break;
    }
}

function updateReceivedTargetsCount() {
    const count = receivedTargetsList ? receivedTargetsList.children.length : 0;
    const textElement = document.getElementById('received-targets-text');
    if (textElement) {
        textElement.textContent = `${count} Received`;
    }
}

function updateMortarReadyStatus() {
    const statusElement = document.getElementById('mortar-ready-status');
    const iconElement = document.getElementById('ready-icon');
    const textElement = document.getElementById('ready-text');

    if (!statusElement) return;

    // Remove all status classes
    statusElement.classList.remove('ready', 'not-ready', 'error');

    // Check if mortar is ready (has position, weapon, and ammo selected)
    const hasPosition = mortarPosition !== null;
    const hasWeapon = selectedMortarType !== null;
    const hasAmmo = selectedAmmoType !== null;

    if (hasPosition && hasWeapon && hasAmmo) {
        mortarReadyStatus = 'ready';
        statusElement.classList.add('ready');
        iconElement.textContent = '[RDY]';
        textElement.textContent = 'Ready to Fire';
    } else {
        mortarReadyStatus = 'not-ready';
        statusElement.classList.add('not-ready');
        iconElement.textContent = '[!]';

        const missing = [];
        if (!hasPosition) missing.push('Position');
        if (!hasWeapon) missing.push('Weapon');
        if (!hasAmmo) missing.push('Ammo');

        textElement.textContent = `Need: ${missing.join(', ')}`;
    }
}

function updateGridCoordinates(lat, lng) {
    const textElement = document.getElementById('coordinates-text');
    if (textElement) {
        if (lat !== undefined && lng !== undefined) {
            textElement.textContent = `${lat.toFixed(4)}°, ${lng.toFixed(4)}°`;
        } else {
            textElement.textContent = '--°, --°';
        }
    }
}

function initializeStatusBar() {
    // Initialize connection status
    updateConnectionStatus('connecting');

    // Initialize received targets count
    updateReceivedTargetsCount();

    // Initialize mortar ready status
    updateMortarReadyStatus();

    // Initialize coordinates
    updateGridCoordinates();

    // Set up mouse move listener for coordinates
    if (mortarMap) {
        mortarMap.on('mousemove', function(e) {
            updateGridCoordinates(e.latlng.lat, e.latlng.lng);
        });

        mortarMap.on('mouseout', function() {
            updateGridCoordinates();
        });
    }

    // Simulate connection after a delay
    setTimeout(() => {
        updateConnectionStatus('connected');
    }, 2000);
}

// --- Calculation Helper Functions ---
function toRadians(degrees) { return degrees * Math.PI / 180; }
function toDegrees(radians) { return radians * 180 / Math.PI; }

// --- Target Fetching and Display ---

// Clears existing targets from the list and map
function clearDisplayedTargets() {
    console.log("Clearing displayed targets...");
    displayedMarkers.forEach(marker => {
        if (mortarMap.hasLayer(marker)) {
            mortarMap.removeLayer(marker);
        }
    });
    displayedMarkers = [];
    if (receivedTargetsList) receivedTargetsList.innerHTML = ''; // Clear list content
    selectedTargetData = null;
    // Remove highlight from previously selected item
    if (selectedListItem) {
        selectedListItem.classList.remove('selected');
        selectedListItem = null;
    }
    // Remove highlight from previously selected marker
    if (selectedTargetMarker) {
        // Reset the marker's icon or style here if you changed it for selection
        // For example, if you used a custom icon for selected:
        // selectedTargetMarker.setIcon(defaultMarkerIcon);
        selectedTargetMarker = null;
    }
    if (solutionDisplay) {
        solutionDisplay.innerHTML = '<p><em>Select position, weapon, ammo, and target to calculate.</em></p>'; // Reset solution display
        solutionDisplay.classList.remove('calculated'); // Remove calculated highlight
    }
    updateCalculateButtonState(); // Update button state
    updateReceivedTargetsCount(); // Update status bar
}

// Displays a single received target on the list and map (with click listener)
function displayReceivedTarget(targetData) {
    console.log("Attempting to display target:", targetData);
    // Validate data
    if (!receivedTargetsList) { console.error("Cannot display target, list element not found."); return; }
    if (!targetData || !targetData.latlng || typeof targetData.latlng.lat !== 'number' || typeof targetData.latlng.lng !== 'number') {
        console.warn("Received invalid target data structure in displayReceivedTarget:", targetData);
        return;
    }
    // Destructure with defaults
    const { forceType = 'unknown', targetType = 'unknown', quantity = '?', locationText = 'N/A', gridInfo = '', latlng } = targetData;
    const leafletLatLng = L.latLng(latlng.lat, latlng.lng);

    // Create List Item
    const listItem = document.createElement('li');
    listItem.style.cursor = 'pointer'; // Indicate it's clickable
    listItem.targetData = targetData; // Store data on the element
    // Set innerHTML with formatted data
    listItem.innerHTML = `
        <span><strong>Force:</strong> ${forceType}</span>
        <span><strong>Type:</strong> ${targetType}</span>
        <span><strong>Qty:</strong> ${quantity}</span>
        <span><strong>Loc:</strong> ${locationText}${gridInfo}</span>
    `;
    receivedTargetsList.appendChild(listItem);

    // Add click listener with debugging
    listItem.addEventListener('click', function(event) {
        console.log("🔥 CLICK EVENT FIRED on target list item!");
        console.log("Event:", event);
        console.log("Target data on element:", this.targetData);
        handleTargetSelection(event);
    });

    // Also add a test click listener to verify events work
    listItem.addEventListener('mouseenter', function() {
        console.log("🖱️ Mouse entered target item");
        this.style.backgroundColor = 'rgba(29, 53, 87, 0.1)';
    });

    listItem.addEventListener('mouseleave', function() {
        console.log("🖱️ Mouse left target item");
        if (!this.classList.contains('selected')) {
            this.style.backgroundColor = '';
        }
    });

    console.log("✅ Appended list item with click listener:", listItem.innerHTML);
    console.log("✅ Target data stored:", listItem.targetData);

    // Create Map Marker
    let markerColor = forceType === 'enemy' ? 'red' : 'blue';
    let markerShapeHTML = '';
    const markerSize = 16; // Reduced from 20px to 16px

    // Determine marker HTML based on type with distinctive shapes
    // Use solid color based on force type (no white background for enemy)
    const bgColor = markerColor; // Always use the marker color (red or blue)
    const borderStyle = `1px solid ${forceType === 'enemy' ? '#990000' : '#000066'}`; // Darker border for definition

    // Common size for all icons
    const iconWidth = markerSize;
    const iconHeight = markerSize;

    switch (targetType) {
        case 'infantry':
            // Circle
            markerShapeHTML = `
                <div style="border: ${borderStyle}; background-color: ${bgColor}; width: ${iconWidth}px; height: ${iconHeight}px; border-radius: 50%;"></div>
            `; break;
        case 'tank':
            // Triangle
            markerShapeHTML = `
                <div style="position: relative; width: ${iconWidth}px; height: ${iconHeight}px;">
                    <div style="width: 0; height: 0; border-left: ${iconWidth/2}px solid transparent; border-right: ${iconWidth/2}px solid transparent; border-bottom: ${iconHeight}px solid ${bgColor}; position: relative; border-top: 0; outline: ${borderStyle}; outline-offset: -1px;"></div>
                </div>
            `; break;
        case 'mg':
            // Square
            markerShapeHTML = `
                <div style="border: ${borderStyle}; background-color: ${bgColor}; width: ${iconWidth}px; height: ${iconHeight}px;"></div>
            `; break;
        case 'artillery':
            // Diamond
            markerShapeHTML = `
                <div style="position: relative; width: ${iconWidth}px; height: ${iconHeight}px;">
                    <div style="position: absolute; width: ${iconWidth}px; height: ${iconHeight}px; background-color: ${bgColor}; transform: rotate(45deg); border: ${borderStyle};"></div>
                </div>
            `; break;
        case 'antiair':
            // Diamond with cross
            markerShapeHTML = `
                <div style="position: relative; width: ${iconWidth}px; height: ${iconHeight}px;">
                    <div style="position: absolute; width: ${iconWidth}px; height: ${iconHeight}px; background-color: ${bgColor}; transform: rotate(45deg); border: ${borderStyle};"></div>
                    <div style="position: absolute; top: ${iconHeight/2-0.5}px; left: 0; width: 100%; height: 1px; background-color: ${markerColor};"></div>
                    <div style="position: absolute; top: 0; left: ${iconWidth/2-0.5}px; width: 1px; height: 100%; background-color: ${markerColor};"></div>
                </div>
            `; break;
        case 'recon':
            // Hexagon
            markerShapeHTML = `
                <div style="position: relative; width: ${iconWidth}px; height: ${iconHeight}px;">
                    <svg width="${iconWidth}" height="${iconHeight}" viewBox="0 0 ${iconWidth} ${iconHeight}" style="position: absolute; top: 0; left: 0;">
                        <polygon points="${iconWidth*0.25},0 ${iconWidth*0.75},0 ${iconWidth},${iconHeight*0.5} ${iconWidth*0.75},${iconHeight} ${iconWidth*0.25},${iconHeight} 0,${iconHeight*0.5}" fill="${bgColor}" stroke="${markerColor}" stroke-width="2" />
                    </svg>
                </div>
            `; break;
        case 'apc':
            // Rectangle with rounded corners
            markerShapeHTML = `
                <div style="border: ${borderStyle}; background-color: ${bgColor}; width: ${iconWidth*1.2}px; height: ${iconHeight*0.8}px; border-radius: 5px;"></div>
            `; break;
        case 'heli':
            // Pentagon
            markerShapeHTML = `
                <div style="position: relative; width: ${iconWidth}px; height: ${iconHeight}px;">
                    <svg width="${iconWidth}" height="${iconHeight}" viewBox="0 0 ${iconWidth} ${iconHeight}" style="position: absolute; top: 0; left: 0;">
                        <polygon points="${iconWidth/2},0 ${iconWidth},${iconHeight/3} ${iconWidth*0.8},${iconHeight} ${iconWidth*0.2},${iconHeight} 0,${iconHeight/3}" fill="${bgColor}" stroke="${markerColor}" stroke-width="2" />
                    </svg>
                </div>
            `; break;
        case 'command':
            // Star
            markerShapeHTML = `
                <div style="position: relative; width: ${iconWidth}px; height: ${iconHeight}px;">
                    <svg width="${iconWidth}" height="${iconHeight}" viewBox="0 0 ${iconWidth} ${iconHeight}" style="position: absolute; top: 0; left: 0;">
                        <polygon points="${iconWidth/2},0 ${iconWidth*0.65},${iconHeight*0.35} ${iconWidth},${iconHeight*0.35} ${iconWidth*0.75},${iconHeight*0.6} ${iconWidth*0.85},${iconHeight} ${iconWidth/2},${iconHeight*0.75} ${iconWidth*0.15},${iconHeight} ${iconWidth*0.25},${iconHeight*0.6} 0,${iconHeight*0.35} ${iconWidth*0.35},${iconHeight*0.35}" fill="${bgColor}" stroke="${markerColor}" stroke-width="2" />
                    </svg>
                </div>
            `; break;
        default: // Fallback for unknown types
            markerShapeHTML = `
                <div style="border: ${borderStyle}; background-color: ${bgColor}; width: ${iconWidth}px; height: ${iconHeight}px; border-radius: 50%;"></div>
            `; break;
    }
    try {
        // Create the Leaflet marker
        const marker = L.marker(leafletLatLng, {
            title: `${forceType} ${targetType} (${quantity})`, // Tooltip on hover
            icon: L.divIcon({
                className: `target-marker target-${forceType} target-${targetType}`,
                iconSize: [markerSize, markerSize],
                iconAnchor: targetType === 'tank' ? [markerSize / 2, markerSize] : [markerSize / 2, markerSize / 2], // Correct anchor
                html: markerShapeHTML
            })
        }).addTo(mortarMap); // Add marker to map
        displayedMarkers.push(marker); // Add to array TO BE CLEARED later
        listItem.marker = marker; // Associate marker if needed later
        console.log("Added target marker to map at", leafletLatLng);
    } catch (markerError) {
        console.error("!!! ERROR adding target marker to map:", markerError, "for target:", targetData);
    }
}

// Fetches targets and calls display function
async function fetchAndDisplayTargets() {
    console.log("--- fetchAndDisplayTargets START ---");
    if (!receivedTargetsList) { console.error("Target list element not found! Cannot fetch."); return; }
    if(refreshButton) refreshButton.disabled = true;
    // Set initial loading message only if list is currently empty or showing the default message
    if (!receivedTargetsList.querySelector('li') || receivedTargetsList.innerHTML.includes('Waiting for target data...')) {
        receivedTargetsList.innerHTML = '<li><em style="color: grey;">Fetching target data...</em></li>';
    }

    try {
        let targets;

        if (DEMO_MODE) {
            // Demo mode - get targets from localStorage (shared between screens)
            console.log("Demo mode: Getting targets from localStorage");
            await new Promise(resolve => setTimeout(resolve, 800)); // Simulate network delay

            const storedTargets = localStorage.getItem('fscsObserverTargets');
            if (storedTargets) {
                targets = JSON.parse(storedTargets);
                console.log("Demo: Retrieved targets from localStorage:", targets);
            } else {
                targets = [];
                console.log("Demo: No targets found in localStorage");
            }
        } else {
            // Production mode - fetch from server
            const response = await fetch(fetchApiUrl);
            console.log("Fetch response status:", response.status);
            if (!response.ok) throw new Error(`HTTP error! Status: ${response.status} ${response.statusText}`);
            targets = await response.json();
        }

        console.log("Received data:", JSON.stringify(targets, null, 2));

        clearDisplayedTargets(); // Clear previous markers and list items NOW

        if (targets && Array.isArray(targets) && targets.length > 0) {
            targets.forEach(target => displayReceivedTarget(target)); // Call the function that ADDS MARKERS
            console.log(`Finished Displaying ${targets.length} targets.`);
            receivedTargetsList.scrollTop = 0; // Scroll list to top

            // Update status for demo
            if (DEMO_MODE) {
                updateConnectionStatus('connected');
                receivedTargetsList.innerHTML += '<li style="color: #4ade80; font-style: italic; margin-top: 10px;">📡 Demo Mode: Targets loaded from Observer screen</li>';
            }
        } else {
            const message = DEMO_MODE ?
                'No targets available. Add targets in Observer screen and send them first.' :
                'No current targets received from server.';
            receivedTargetsList.innerHTML = `<li>${message}</li>`;
            console.log("No targets received or empty array.");
        }
    } catch (error) {
        console.error("!!! ERROR in fetchAndDisplayTargets:", error);
        // Display error in the list area if it's not already showing one
        if (!receivedTargetsList.innerHTML.includes('Error')) {
            clearDisplayedTargets(); // Clear before showing error
            const errorMessage = DEMO_MODE ?
                `Demo Mode Error: ${error.message}. Try adding targets in Observer screen first.` :
                `Error fetching targets: ${error.message}`;
            receivedTargetsList.innerHTML = `<li style="color: red;">${errorMessage}</li>`;
        }
    } finally {
        if(refreshButton) refreshButton.disabled = false;
        console.log("--- fetchAndDisplayTargets END ---");
    }
}

// --- Mortar Position Functions ---
function plotMortarPosition() {
    console.log("Plotting mortar position:", mortarPosition);
    if (!mortarPosition) return;
    const iconSize = 16; // Reduced from 24px to 16px
    const mortarIcon = L.divIcon({
        className: 'mortar-position-marker',
        iconSize: [iconSize, iconSize],
        iconAnchor: [iconSize / 2, iconSize / 2],
        html: `<div style="background-color: darkgreen; width: ${iconSize}px; height: ${iconSize}px; border-radius: 50%; border: 1px solid white;"></div>`
    });
    if (mortarPositionMarker) {
        console.log("Removing previous mortar marker.");
        mortarMap.removeLayer(mortarPositionMarker);
    }
    try {
        mortarPositionMarker = L.marker(mortarPosition, { icon: mortarIcon, title: 'Mortar Position' }).addTo(mortarMap);
        mortarPositionMarker.bindPopup(`<b>Mortar Position</b><br>${mortarPosition.lat.toFixed(6)}, ${mortarPosition.lng.toFixed(6)}`).openPopup();
        console.log("Mortar marker plotted successfully.");
    } catch(e) {
        console.error("!!! ERROR plotting mortar marker:", e);
    }
}

function setMortarPositionFromLatLng(latlng) {
    console.log("Setting mortar position from LatLng:", latlng);
    if (!latlng) return;
    mortarPosition = latlng;
    // Using Lat/Lon display only
    if (currentMortarPosDisplay) {
        currentMortarPosDisplay.textContent = `Current Position: Lat: ${mortarPosition.lat.toFixed(6)}, Lon: ${mortarPosition.lng.toFixed(6)}`;
    } else { console.error("Cannot find currentMortarPosDisplay element"); }
    plotMortarPosition();
    if (mortarPosInput) { mortarPosInput.value = `Lat: ${mortarPosition.lat.toFixed(6)}, Lon: ${mortarPosition.lng.toFixed(6)}`; }
    updateCalculateButtonState(); // Update button state
    updateMortarReadyStatus(); // Update status bar

    // Update range rings if they're enabled
    if (showRangeRings) {
        updateRangeRings();
    }
}

// --- Weapon Selection Functions ---
function updateAmmoOptions() {
    if (!mortarTypeSelect || !ammoTypeSelect) { console.error("Dropdown elements not found!"); return; }
    selectedMortarType = mortarTypeSelect.value;
    selectedAmmoType = null; // Reset ammo selection
    ammoTypeSelect.innerHTML = ''; // Clear existing options
    console.log("Updating ammo options for mortar:", selectedMortarType);

    const currentAmmoList = mortarAmmoData[selectedMortarType];

    if (selectedMortarType && currentAmmoList) {
        const defaultOption = document.createElement('option'); defaultOption.value = ""; defaultOption.textContent = "-- Select Ammo --"; defaultOption.disabled = true; defaultOption.selected = true; ammoTypeSelect.appendChild(defaultOption);
        currentAmmoList.forEach(ammo => { const option = document.createElement('option'); option.value = ammo; option.textContent = ammo; ammoTypeSelect.appendChild(option); });
        ammoTypeSelect.disabled = false; // Enable the dropdown
        console.log("Ammo dropdown enabled and populated.");
    } else {
        const defaultOption = document.createElement('option'); defaultOption.value = ""; defaultOption.textContent = selectedMortarType ? "-- No Ammo Data --" : "-- Select Mortar First --"; defaultOption.disabled = true; defaultOption.selected = true; ammoTypeSelect.appendChild(defaultOption);
        ammoTypeSelect.disabled = true; // Disable the dropdown
        console.log("Ammo dropdown disabled.");
    }
    updateCalculateButtonState(); // Update button state
    updateMortarReadyStatus(); // Update status bar
}

function handleAmmoSelection() {
    if (!ammoTypeSelect) return;
    selectedAmmoType = ammoTypeSelect.value;
    console.log("Selected Ammo:", selectedAmmoType || "None");
    updateCalculateButtonState(); // Update button state
    updateMortarReadyStatus(); // Update status bar
}

// --- Target Selection Function ---
function handleTargetSelection(event) {
    console.log("🎯 Target selection clicked!");
    const clickedLi = event.currentTarget;
    const targetData = clickedLi.targetData; // Retrieve stored data

    console.log("Clicked LI:", clickedLi);
    console.log("Target data:", targetData);

    if (!targetData) {
        console.error("❌ No target data found on list item.");
        return;
    }

    // Remove highlight from previously selected item and marker
    if (selectedListItem) {
        selectedListItem.classList.remove('selected');
        console.log("Removed selection from previous item");
    }

    if (selectedTargetMarker) {
        // Reset the previous marker opacity
        selectedTargetMarker.setOpacity(1.0);
        console.log("Reset previous marker opacity");
    }

    // Highlight the new item and store its data/reference
    clickedLi.classList.add('selected');
    selectedListItem = clickedLi;
    selectedTargetData = targetData; // Store the data object

    console.log("✅ Target selected:", selectedTargetData);

    // Highlight the corresponding marker on the map
    // Find the marker that matches the selected list item's target data location
    selectedTargetMarker = displayedMarkers.find(marker =>
        marker.getLatLng().equals(L.latLng(selectedTargetData.latlng.lat, selectedTargetData.latlng.lng))
    );

    if (selectedTargetMarker) {
        // Add a visual cue to the selected marker
        selectedTargetMarker.setOpacity(0.8); // Make it slightly more transparent
        console.log("✅ Marker highlighted on map");
    } else {
        console.warn("⚠️ Could not find corresponding marker for selected target");
    }

    // Update the calculate button state
    updateCalculateButtonState();

    // Show visual feedback that target is selected
    if (solutionDisplay) {
        solutionDisplay.innerHTML = `
            <p style="color: var(--accent-color);">
                <strong>🎯 Target Selected:</strong> ${selectedTargetData.targetType} (${selectedTargetData.quantity})<br>
                <strong>Location:</strong> ${selectedTargetData.locationText}<br>
                <em>Ready to calculate firing solution...</em>
            </p>
        `;
    }

    console.log("🎯 Target selection complete. Selected target:", selectedTargetData);

    // Optionally pan map
    if (selectedTargetData.latlng) {
        try {
            mortarMap.setView([selectedTargetData.latlng.lat, selectedTargetData.latlng.lng], 16); // Zoom a bit closer
        } catch(e) {
            console.error("Error panning map to target:", e);
        }
    }
    updateCalculateButtonState(); // Update button state
}

// --- Ballistic Calculation & Display ---

// Function to check if calculation is possible and enable/disable button
function updateCalculateButtonState() {
    if (!calculateButton) { /*console.log("Calc button not found for state update.");*/ return; }
    const ready = mortarPosition instanceof L.LatLng &&
                  selectedMortarType &&
                  selectedAmmoType && // Check if truthy (not null or empty string)
                  selectedTargetData &&
                  selectedTargetData.latlng;
    // console.log(`Button State Update: MP=${!!mortarPosition}, MT=${!!selectedMortarType}, AT=${!!selectedAmmoType}, TD=${!!selectedTargetData} => Ready=${ready}`);
    calculateButton.disabled = !ready;
    calculateButton.title = ready ? "Calculate firing solution" : "Set position, select weapon/ammo, and select target first";
}

// ** Calculation Function **
function calculateAndDisplaySolution() {
    console.log("--- calculateAndDisplaySolution START ---");
    // Log inputs for debugging
    console.log("Calculate inputs:", { mortarPosition, selectedMortarType, selectedAmmoType, selectedTargetData });

    // Enhanced validation with user feedback
    const validation = validateMortarSetup();
    if (!validation.isValid || !selectedTargetData || !selectedTargetData.latlng) {
        showMortarValidationFeedback();
        if (solutionDisplay) {
            solutionDisplay.innerHTML = '<p style="color: orange;">[ERROR] Cannot calculate: Setup incomplete or no target selected.</p>';
            solutionDisplay.classList.remove('calculated');
        }
        return;
    }

    // Show loading state
    const calculateButton = document.getElementById('calculate-solution-button');
    const originalText = calculateButton.textContent;
    calculateButton.disabled = true;
    calculateButton.innerHTML = '<span class="loading-spinner">[CALCULATING]</span> Calculating...';
    calculateButton.classList.add('calculating');

    if (solutionDisplay) {
        solutionDisplay.innerHTML = '<p style="color: #1565c0;">[PROCESSING] Calculating firing solution...</p>';
        solutionDisplay.classList.remove('calculated');
    }

    // Simulate calculation delay for better UX
    setTimeout(() => {

    const mortarLat = mortarPosition.lat;
    const mortarLng = mortarPosition.lng;
    const targetLat = selectedTargetData.latlng.lat;
    const targetLng = selectedTargetData.latlng.lng;

    // 1. Calculate Distance (using Haversine formula for accuracy)
    const R = 6371e3; // metres
    const φ1 = toRadians(mortarLat);
    const φ2 = toRadians(targetLat);
    const Δφ = toRadians(targetLat - mortarLat);
    const Δλ = toRadians(targetLng - mortarLng);

    const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    const distanceMeters = R * c;
    const distanceYards = distanceMeters * 1.09361;

    // 2. Calculate Bearing (initial bearing from mortar to target)
    const y = Math.sin(Δλ) * Math.cos(φ2);
    const x = Math.cos(φ1) * Math.sin(φ2) -
              Math.sin(φ1) * Math.cos(φ2) * Math.cos(Δλ);
    let bearingDegrees = toDegrees(Math.atan2(y, x));
    if (bearingDegrees < 0) {
        bearingDegrees += 360; // Normalize to 0-360 degrees
    }

    // ** 3. Ballistic Calculations (Placeholder - Requires Real Data) **
    // This is where you would use the selectedMortarType and selectedAmmoType
    // to look up ballistic data (e.g., from a range table or a more complex model).
    // For now, we'll just provide a placeholder.

    let elevationDegrees = 'N/A';
    let deflectionMils = 'N/A';

    // Example of how you might use the data (this is highly simplified and likely incorrect for real-world ballistics):
    if (selectedMortarType === '60mm M224' && selectedAmmoType.includes('HE')) {
        // In a real scenario, you'd have a function or data structure that maps
        // distance to elevation and potentially deflection for each ammo type.
        if (distanceYards < 500) {
            elevationDegrees = 60; // Example value
            deflectionMils = 0; // Example value
        } else if (distanceYards < 1000) {
            elevationDegrees = 45; // Example value
            deflectionMils = 2; // Example value
        } else {
            elevationDegrees = 30; // Example value
            deflectionMils = -1; // Example value
        }
    } else if (selectedMortarType === '81mm M252' && selectedAmmoType.includes('HE')) {
        // Similar logic for other mortar/ammo types
        elevationDegrees = 50; // Example
        deflectionMils = 1; // Example
    } else {
        elevationDegrees = 'Ballistic data not available for this combination.';
        deflectionMils = 'N/A';
    }

        // 4. Display the Solution with enhanced feedback
        if (solutionDisplay) {
            solutionDisplay.innerHTML = `
                <h3>[SOLUTION] Firing Solution</h3>
                <p><strong>Target:</strong> ${selectedTargetData.targetType} (${selectedTargetData.quantity})</p>
                <p><strong>Distance:</strong> ${distanceYards.toFixed(2)} yards (${distanceMeters.toFixed(2)} meters)</p>
                <p><strong>Bearing (Grid):</strong> ${bearingDegrees.toFixed(2)} degrees</p>
                <p><strong>Elevation:</strong> ${elevationDegrees} degrees</p>
                <p><strong>Deflection:</strong> ${deflectionMils} mils</p>
                <p class="calculation-confirmation">[READY] Solution Ready for Fire Mission</p>
            `;
            solutionDisplay.classList.add('calculated');
        }

        // Reset button state
        calculateButton.disabled = false;
        calculateButton.classList.remove('calculating');
        calculateButton.innerHTML = '[COMPLETE] Solution Ready';

        // Show temporary success state
        setTimeout(() => {
            calculateButton.innerHTML = originalText;
        }, 2000);

        console.log("Calculated Solution:", { distanceYards, bearingDegrees, elevationDegrees, deflectionMils });
        console.log("--- calculateAndDisplaySolution END ---");
    }, 1500); // End of setTimeout for calculation delay
}

// --- Event Listeners and Initial Load ---
// Function to set up collapsible legend
function setupCollapsibleLegend() {
    const mapLegend = document.getElementById('map-legend');
    if (!mapLegend) return;

    const legendHeader = mapLegend.querySelector('h4');
    if (!legendHeader) return;

    // Set initial state (expanded by default)
    mapLegend.classList.add('expanded');

    // Toggle legend on header click
    legendHeader.addEventListener('click', () => {
        if (mapLegend.classList.contains('expanded')) {
            mapLegend.classList.remove('expanded');
            mapLegend.classList.add('collapsed');
        } else {
            mapLegend.classList.remove('collapsed');
            mapLegend.classList.add('expanded');
        }
    });
}

function initializeMortarApp() {
    console.log("Initializing Mortar App event listeners and initial state...");

    // Debug: Check if elements exist
    console.log("🔍 Debug - Element check:");
    console.log("receivedTargetsList:", receivedTargetsList);
    console.log("calculateButton:", calculateButton);
    console.log("solutionDisplay:", solutionDisplay);

    // Use event delegation for target selection
    if (receivedTargetsList) {
        receivedTargetsList.addEventListener('click', function(event) {
            console.log("🔥 CLICK on targets list container!");
            console.log("Event target:", event.target);
            console.log("Event currentTarget:", event.currentTarget);

            // Find the closest li element
            const clickedLi = event.target.closest('li');
            console.log("Closest LI:", clickedLi);

            if (clickedLi && clickedLi.targetData) {
                console.log("🎯 Found target data via delegation:", clickedLi.targetData);
                // Create a fake event object for the handler
                const fakeEvent = { currentTarget: clickedLi };
                handleTargetSelection(fakeEvent);
            } else {
                console.log("❌ No target data found via delegation");
            }
        });
    }

    // Ensure map is available before attaching listener
    if (mortarMap) {
        mortarMap.on('click', (e) => { setMortarPositionFromLatLng(e.latlng); });
        console.log("Map click listener attached.");
    } else { console.error("Map not initialized before attaching map click listener."); }

    // Check elements before adding listeners
    if (mortarTypeSelect) {
        mortarTypeSelect.addEventListener('change', updateAmmoOptions);
        console.log("Mortar type listener attached.");
    } else { console.error("Mortar type select not found!"); }

    if (ammoTypeSelect) {
        ammoTypeSelect.addEventListener('change', handleAmmoSelection);
        console.log("Ammo type listener attached.");
    } else { console.error("Ammo type select not found!"); }

    if (refreshButton) {
        refreshButton.addEventListener('click', fetchAndDisplayTargets);
        console.log("Refresh button listener attached.");
    } else { console.error("Refresh button not found!"); }

    if (calculateButton) {
        // Remove any existing listeners first
        calculateButton.removeEventListener('click', calculateAndDisplaySolution);

        // Add the click listener
        calculateButton.addEventListener('click', function(event) {
            console.log("🔥 CALCULATE BUTTON CLICKED!");
            console.log("Event:", event);
            console.log("Button disabled:", calculateButton.disabled);

            if (!calculateButton.disabled) {
                console.log("✅ Button is enabled, calling calculation function...");
                calculateAndDisplaySolution();
            } else {
                console.log("❌ Button is disabled, cannot calculate");
            }
        });

        // Also add a test click listener to verify events work
        calculateButton.addEventListener('mouseenter', function() {
            console.log("🖱️ Mouse entered calculate button");
        });

        console.log("Calculate button listener attached with debugging.");
    } else { console.error("Calculate Solution button not found!"); }

    if (clearTargetsButton) {
        clearTargetsButton.addEventListener('click', clearDisplayedTargets);
        console.log("Clear targets button listener attached.");
    } else { console.error("Clear targets button not found!"); }

    // Add event listeners for button active states (for glow animation)
    const buttons = document.querySelectorAll('button');
    buttons.forEach(button => {
        button.addEventListener('mousedown', () => {
            if (!button.disabled) {
                 // Determine glow class based on button background color or ID
                 let glowClass = 'button-glow'; // Default glow
                 if (button.id === 'add-target-button' || button.id === 'calculate-solution-button') {
                     glowClass = 'glow-green';
                 } else if (button.id === 'send-targets-button' || button.id === 'refresh-targets-button') {
                     glowClass = 'glow-primary';
                 } else if (button.id === 'clear-targets-button' || button.id === 'clear-targets-button-mortar' || button.classList.contains('remove-button')) {
                      glowClass = 'glow-danger';
                 }
                button.classList.add(glowClass);
            }
        });
        button.addEventListener('mouseup', () => {
             // Remove all potential glow classes on mouse up
            button.classList.remove('button-glow', 'glow-green', 'glow-primary', 'glow-danger');
        });
         // Also remove glow if mouse leaves the button while still pressed
        button.addEventListener('mouseleave', () => {
             button.classList.remove('button-glow', 'glow-green', 'glow-primary', 'glow-danger');
         });
    });


    // Initial setup calls
    try {
        fetchAndDisplayTargets(); // Initial fetch
        updateAmmoOptions();     // Set initial ammo dropdown state
        updateCalculateButtonState(); // Set initial calculate button state
        setupCollapsibleLegend(); // Set up collapsible legend
    } catch(e) {
        console.error("Error during initial setup calls:", e);
    }
    console.log("Initial setup calls finished.");
}

// --- Form Validation Functions for Mortar Screen ---
function validateMortarSetup() {
    const hasPosition = mortarPosition !== null;
    const hasWeapon = selectedMortarType !== null && selectedMortarType !== '';
    const hasAmmo = selectedAmmoType !== null && selectedAmmoType !== '';

    return {
        isValid: hasPosition && hasWeapon && hasAmmo,
        missing: {
            position: !hasPosition,
            weapon: !hasWeapon,
            ammo: !hasAmmo
        }
    };
}

function showMortarValidationFeedback() {
    const validation = validateMortarSetup();
    const calculateButton = document.getElementById('calculate-solution-button');

    if (!validation.isValid) {
        const missing = [];
        if (validation.missing.position) missing.push('Position');
        if (validation.missing.weapon) missing.push('Weapon');
        if (validation.missing.ammo) missing.push('Ammunition');

        // Show validation message
        const message = `Setup required: ${missing.join(', ')}`;
        showTemporaryMessage(calculateButton, message, 'warning');
    }
}

function showTemporaryMessage(element, message, type = 'info') {
    // Create or update message element
    let messageEl = element.parentNode.querySelector('.temp-message');
    if (!messageEl) {
        messageEl = document.createElement('div');
        messageEl.className = 'temp-message';
        element.parentNode.appendChild(messageEl);
    }

    messageEl.textContent = message;
    messageEl.className = `temp-message ${type} show`;

    // Auto-hide after 3 seconds
    setTimeout(() => {
        messageEl.classList.remove('show');
        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.parentNode.removeChild(messageEl);
            }
        }, 300);
    }, 3000);
}

// --- Tooltip Setup for Mortar Screen ---
function setupMortarTooltips() {
    // Add tooltips to mortar-specific elements
    const mortarPosInput = document.getElementById('mortar-position');
    if (mortarPosInput) {
        mortarPosInput.title = 'Click on the map to set mortar position, or enter coordinates manually';
    }

    const mortarTypeSelect = document.getElementById('mortar-type');
    if (mortarTypeSelect) {
        mortarTypeSelect.title = 'Select the type and caliber of mortar weapon system';
    }

    const ammoTypeSelect = document.getElementById('ammo-type');
    if (ammoTypeSelect) {
        ammoTypeSelect.title = 'Select ammunition type: HE (High Explosive), Smoke, Illumination, etc.';
    }

    const solutionDisplay = document.getElementById('solution-display');
    if (solutionDisplay) {
        solutionDisplay.title = 'Calculated firing solution with bearing, elevation, and deflection data';
    }

    // Add tooltips to received targets list
    const receivedTargetsList = document.getElementById('received-targets-list');
    if (receivedTargetsList) {
        receivedTargetsList.title = 'Targets received from observer teams - click to select for fire mission';
    }
}

// --- Range Rings Functions ---
function toggleRangeRings() {
    showRangeRings = !showRangeRings;
    const button = document.getElementById('toggle-range-rings');

    if (showRangeRings) {
        button.textContent = '🎯 Hide Range Rings';
        button.classList.add('active');
        updateRangeRings();
    } else {
        button.textContent = '🎯 Show Range Rings';
        button.classList.remove('active');
        clearRangeRings();
    }
}

function clearRangeRings() {
    rangeRings.forEach(ring => {
        if (mortarMap.hasLayer(ring)) {
            mortarMap.removeLayer(ring);
        }
    });
    rangeRings = [];
}

function updateRangeRings() {
    if (!mortarPosition || !selectedMortarType) return;

    // Clear existing rings
    clearRangeRings();

    // Get range data for selected mortar type
    const rangeData = getMortarRangeData(selectedMortarType);
    if (!rangeData) return;

    // Create range rings
    rangeData.ranges.forEach((range, index) => {
        const ring = L.circle(mortarPosition, {
            radius: range.meters,
            fillColor: range.color,
            color: range.color,
            weight: 2,
            opacity: 0.7,
            fillOpacity: 0.1,
            dashArray: index === rangeData.ranges.length - 1 ? '5, 5' : null // Dashed line for max range
        }).addTo(mortarMap);

        // Add range label
        ring.bindTooltip(`${range.label}: ${range.meters}m`, {
            permanent: false,
            direction: 'center',
            className: 'range-tooltip'
        });

        rangeRings.push(ring);
    });
}

function getMortarRangeData(mortarType) {
    const rangeData = {
        '60mm M224': {
            ranges: [
                { label: 'Minimum Range', meters: 70, color: '#ff4444' },
                { label: 'Effective Range', meters: 3500, color: '#44ff44' },
                { label: 'Maximum Range', meters: 3490, color: '#ffaa44' }
            ]
        },
        '81mm M252': {
            ranges: [
                { label: 'Minimum Range', meters: 83, color: '#ff4444' },
                { label: 'Effective Range', meters: 5650, color: '#44ff44' },
                { label: 'Maximum Range', meters: 5935, color: '#ffaa44' }
            ]
        },
        '120mm M120/M121': {
            ranges: [
                { label: 'Minimum Range', meters: 200, color: '#ff4444' },
                { label: 'Effective Range', meters: 7200, color: '#44ff44' },
                { label: 'Maximum Range', meters: 7240, color: '#ffaa44' }
            ]
        }
    };

    return rangeData[mortarType] || null;
}

// --- Retro Mode Functions ---
function toggleRetroMode() {
    const body = document.body;

    // Toggle retro mode class
    body.classList.toggle('retro-mode');

    // Update button text
    const themeToggle = document.getElementById('retro-mode-toggle');
    if (themeToggle) {
        themeToggle.textContent = body.classList.contains('retro-mode')
            ? 'Standard Mode'
            : 'Retro Mode';
    }

    // Save preference to localStorage
    localStorage.setItem('vectorFireTheme', body.classList.contains('retro-mode') ? 'retro' : 'standard');

    // Play a retro toggle sound
    playToggleSound();
}

function playToggleSound() {
    try {
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.type = 'square';
        oscillator.frequency.setValueAtTime(document.body.classList.contains('retro-mode') ? 440 : 880, audioContext.currentTime);

        gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        oscillator.start();
        oscillator.stop(audioContext.currentTime + 0.3);
    } catch (e) {
        console.log('Audio not supported or enabled');
    }
}

function initializeTheme() {
    // Load saved theme preference
    const savedTheme = localStorage.getItem('vectorFireTheme');
    if (savedTheme === 'retro') {
        document.body.classList.add('retro-mode');
        const themeToggle = document.getElementById('retro-mode-toggle');
        if (themeToggle) {
            themeToggle.textContent = 'Standard Mode';
        }
    }
}

function initializeCollapsibles() {
    // Initialize collapsible sections
    const collapsibles = document.querySelectorAll(".collapsible");

    collapsibles.forEach(function(collapsible) {
        collapsible.addEventListener("click", function() {
            this.classList.toggle("active");
            const content = this.nextElementSibling;

            if (content.style.maxHeight) {
                // Collapsing
                content.style.maxHeight = null;
                content.style.visibility = "hidden";
                content.style.opacity = "0";
                content.style.padding = "0";
            } else {
                // Expanding
                content.style.visibility = "visible";
                content.style.opacity = "1";
                content.style.padding = "calc(var(--spacing-unit) * 3.5)";
                content.style.maxHeight = content.scrollHeight + "px";
            }
        });
    });
}

// Wait for DOM Ready before attaching listeners and doing initial fetch
document.addEventListener('DOMContentLoaded', function() {
    // Initialize status bar first
    setTimeout(() => {
        initializeStatusBar();
        initializeTheme(); // Initialize retro mode theme
        initializeCollapsibles(); // Initialize collapsible sections

        // Setup mortar-specific tooltips
        setupMortarTooltips();

        // Load accessibility preferences (reuse from observer)
        if (typeof loadAccessibilityPreferences === 'function') {
            loadAccessibilityPreferences();
        } else {
            // Inline version for mortar screen
            if (localStorage.getItem('high-contrast-mode') === 'true') {
                document.body.classList.add('high-contrast-mode');
            }
            if (localStorage.getItem('night-vision-mode') === 'true') {
                document.body.classList.add('night-vision-mode');
            }
            if (localStorage.getItem('large-touch-mode') === 'true') {
                document.body.classList.add('large-touch-mode');
            }
        }
    }, 100);

    // Then initialize the mortar app
    initializeMortarApp();
});

console.log("--- mortar.js END ---");